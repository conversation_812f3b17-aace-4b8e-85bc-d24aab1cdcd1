/* Modal Styling */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050;
}

.modal-dialog {
    max-width: 1000px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header-background {
    background-color: #f8f9fa;
}

.modal-title {
    margin: 0;
    font-weight: bold;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.5;
}

.btn-close:hover {
    opacity: 1;
}

.modal-body {
    padding: 0;
    max-height: 80vh;
    overflow: hidden;
}

.loading-container {
    text-align: center;
    padding: 2rem;
}

/* Tab Navigation Styling */
.tab-navigation {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    padding: 0 1.5rem;
}

.nav-tabs {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    border-bottom: none;
}

.nav-item {
    margin-bottom: -1px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: #6c757d;
    text-decoration: none;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
    font-weight: 500;
    position: relative;
}

.nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: white;
}

.nav-link.has-changes {
    color: #ffc107;
}

.nav-link.has-changes.active {
    color: #007bff;
}

.unsaved-indicator {
    color: #ffc107;
    font-weight: bold;
    font-size: 1.2rem;
    margin-left: 0.25rem;
}

.nav-link.active .unsaved-indicator {
    color: #007bff;
}

/* Tab Content Styling */
.tab-content {
    height: calc(80vh - 60px);
    overflow-y: auto;
}

.tab-pane {
    padding: 1.5rem;
    height: 100%;
}

.tab-section {
    max-width: 800px;
    margin: 0 auto;
}

.tab-section h4 {
    margin-bottom: 1.5rem;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
}

/* Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group {
    display: flex;
    align-items: center;
}

.input-group-prepend {
    margin-right: 0.5rem;
}

.input-group-text {
    padding: 0.75rem;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 4px 0 0 4px;
}

.country-code {
    flex: 0 0 auto;
    margin-right: 0.5rem;
}

.certification-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    margin: 0;
    transform: scale(1.1);
}

.form-check-label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
}

/* Tab Footer Styling */
.tab-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tab-footer-left {
    display: flex;
    align-items: center;
}

.tab-footer-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Button Styling */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-outline-primary {
    background-color: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline-primary:hover:not(:disabled) {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #545b62;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    background-color: transparent;
    color: #6c757d;
    border: 1px solid #6c757d;
}

.btn-outline-secondary:hover:not(:disabled) {
    background-color: #6c757d;
    color: white;
    transform: translateY(-1px);
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Service Areas Styling */
.service-areas-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.service-area-tag {
    display: inline-flex;
    align-items: center;
    background-color: #007bff;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    gap: 0.5rem;
    font-weight: 500;
}

.service-area-tag-legacy {
    background-color: #ffc107;
    color: #212529;
}

.legacy-indicator {
    font-weight: bold;
    color: #dc3545;
}

.remove-button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 1.2rem;
    line-height: 1;
    padding: 0;
    margin-left: 0.25rem;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Schedule Styling */
.schedule-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.schedule-day-row {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.25rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: white;
    transition: border-color 0.2s ease;
}

.schedule-day-row:hover {
    border-color: #007bff;
}

.day-header {
    display: flex;
    align-items: center;
}

.day-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.1rem;
}

.day-name {
    min-width: 120px;
    color: #495057;
}

.time-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-left: 2rem;
    flex-wrap: wrap;
}

.time-controls.disabled {
    opacity: 0.5;
}

.time-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.time-input-group label {
    font-size: 0.875rem;
    color: #6c757d;
    margin: 0;
    font-weight: 500;
}

.time-input {
    width: 140px;
    padding: 0.5rem;
}

.time-separator {
    font-weight: 600;
    color: #6c757d;
    margin: 0 0.5rem;
    align-self: end;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.validation-error {
    margin-left: 2rem;
    margin-top: 0.5rem;
}

/* Utility Classes */
.text-danger {
    color: #dc3545;
}

.text-muted {
    color: #6c757d;
}

.text-success {
    color: #28a745;
}

.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 4px;
    position: relative;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.75rem 1rem;
    color: inherit;
}

.fade.show {
    opacity: 1;
}

.d-none {
    display: none;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 0 0.5rem;
}

.col-md-5 {
    flex: 0 0 41.666667%;
    padding: 0 0.5rem;
}

.col-md-2 {
    flex: 0 0 16.666667%;
    padding: 0 0.5rem;
}

.d-flex {
    display: flex;
}

.align-items-end {
    align-items: flex-end;
}

.w-100 {
    width: 100%;
}

.my-4 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.me-2 {
    margin-right: 0.5rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-dialog {
        width: 98%;
        margin: 1%;
    }

    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .tab-content {
        height: calc(80vh - 80px);
    }

    .time-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        margin-left: 1rem;
    }

    .time-separator {
        align-self: center;
        margin-bottom: 0;
    }

    .col-md-6,
    .col-md-5,
    .col-md-2 {
        flex: 0 0 100%;
        margin-bottom: 1rem;
    }

    .tab-footer {
        flex-direction: column;
        gap: 1rem;
    }

    .tab-footer-left,
    .tab-footer-right {
        width: 100%;
        justify-content: center;
    }
}
